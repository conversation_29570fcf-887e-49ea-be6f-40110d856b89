body {
    font-family: 'Arial', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: #f0f0f0;
}

.game-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    padding: 20px;
    text-align: center;
    max-width: 800px;
}

h1 {
    color: #333;
    margin-bottom: 20px;
}

.game-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
}

#game-board {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
    margin: 0 auto 20px;
    max-width: 600px;
}

.tile {
    background-color: #4CAF50;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: white;
    transition: all 0.3s;
    aspect-ratio: 1/1;
}

.tile.selected {
    background-color: #FF9800;
    transform: scale(0.95);
}

.tile.removed {
    visibility: hidden;
}

#restart-btn {
    padding: 10px 20px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

#restart-btn:hover {
    background-color: #0b7dda;
}