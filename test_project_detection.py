#!/usr/bin/env python3

import os
import sys
from pathlib import Path
from typing import Dict, Any

def detect_project_type_impl(directory: str = ".") -> Dict[str, Any]:
    """Detect project type based on configuration files and structure."""
    directory = Path(directory).resolve()

    project_info = {
        "directory": str(directory),
        "type": "unknown",
        "detected_files": [],
        "suggested_commands": {},
        "package_managers": []
    }

    # Check for various project configuration files
    config_files = {
        # Python
        "pyproject.toml": {"type": "python", "pm": ["uv", "pip"], "build": None, "run": "python"},
        "requirements.txt": {"type": "python", "pm": ["pip"], "build": None, "run": "python"},
        "setup.py": {"type": "python", "pm": ["pip"], "build": "python setup.py build", "run": "python"},
        "Pipfile": {"type": "python", "pm": ["pipenv"], "build": None, "run": "pipenv run python"},
        "poetry.lock": {"type": "python", "pm": ["poetry"], "build": None, "run": "poetry run python"},

        # Node.js
        "package.json": {"type": "nodejs", "pm": ["npm", "yarn", "pnpm"], "build": "npm run build", "run": "npm start"},
        "yarn.lock": {"type": "nodejs", "pm": ["yarn"], "build": "yarn build", "run": "yarn start"},
        "pnpm-lock.yaml": {"type": "nodejs", "pm": ["pnpm"], "build": "pnpm build", "run": "pnpm start"},

        # Rust
        "Cargo.toml": {"type": "rust", "pm": ["cargo"], "build": "cargo build", "run": "cargo run"},

        # Go
        "go.mod": {"type": "go", "pm": ["go"], "build": "go build", "run": "go run"},

        # Java
        "pom.xml": {"type": "java", "pm": ["maven"], "build": "mvn compile", "run": "mvn exec:java"},
        "build.gradle": {"type": "java", "pm": ["gradle"], "build": "gradle build", "run": "gradle run"},

        # C/C++
        "Makefile": {"type": "c/cpp", "pm": ["make"], "build": "make", "run": "./main"},
        "CMakeLists.txt": {"type": "c/cpp", "pm": ["cmake"], "build": "cmake . && make", "run": "./main"},

        # C#/.NET
        "*.csproj": {"type": "csharp", "pm": ["dotnet"], "build": "dotnet build", "run": "dotnet run"},
        "*.sln": {"type": "csharp", "pm": ["dotnet"], "build": "dotnet build", "run": "dotnet run"},
    }

    detected_types = []

    for file_pattern, info in config_files.items():
        if "*" in file_pattern:
            # Handle glob patterns
            matches = list(directory.glob(file_pattern))
            if matches:
                project_info["detected_files"].extend([str(f.name) for f in matches])
                detected_types.append(info)
        else:
            file_path = directory / file_pattern
            if file_path.exists():
                project_info["detected_files"].append(file_pattern)
                detected_types.append(info)

    if detected_types:
        # Use the first detected type (could be improved with priority logic)
        primary_type = detected_types[0]
        project_info["type"] = primary_type["type"]
        project_info["package_managers"] = primary_type["pm"]

        # Set suggested commands
        if primary_type["build"]:
            project_info["suggested_commands"]["build"] = primary_type["build"]
        if primary_type["run"]:
            project_info["suggested_commands"]["run"] = primary_type["run"]

        # Add install command based on package manager
        if primary_type["pm"]:
            pm = primary_type["pm"][0]  # Use first package manager
            install_commands = {
                "pip": "pip install -r requirements.txt",
                "uv": "uv sync",
                "npm": "npm install",
                "yarn": "yarn install",
                "pnpm": "pnpm install",
                "cargo": "cargo build",
                "go": "go mod tidy",
                "maven": "mvn install",
                "gradle": "gradle build",
                "dotnet": "dotnet restore",
                "pipenv": "pipenv install",
                "poetry": "poetry install"
            }
            if pm in install_commands:
                project_info["suggested_commands"]["install"] = install_commands[pm]

    return project_info

def test_project_detection():
    """Test the project detection functionality."""
    print("🔍 Testing Project Detection Functionality")
    print("=" * 50)
    
    # Test current directory (should detect Python project)
    print("\n📁 Testing current directory:")
    try:
        project_info = detect_project_type_impl(".")
        print(f"  Type: {project_info['type']}")
        print(f"  Detected files: {project_info['detected_files']}")
        print(f"  Package managers: {project_info['package_managers']}")
        print(f"  Suggested commands:")
        for cmd_type, cmd in project_info['suggested_commands'].items():
            print(f"    {cmd_type}: {cmd}")
    except Exception as e:
        print(f"  Error: {e}")
    
    # Test a non-existent directory
    print("\n📁 Testing non-existent directory:")
    try:
        project_info = detect_project_type_impl("./non_existent")
        print(f"  Type: {project_info['type']}")
        print(f"  Detected files: {project_info['detected_files']}")
    except Exception as e:
        print(f"  Error: {e}")
    
    print("\n✅ Project detection test completed!")

if __name__ == "__main__":
    test_project_detection()
