#!/usr/bin/env python3

import os
import sys
import json
import subprocess
import shlex
from pathlib import Path
from textwrap import dedent
from typing import List, Dict, Any, Optional
from openai import OpenAI
from pydantic import BaseModel
from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.style import Style
from rich.live import Live
from rich.text import Text
from prompt_toolkit import PromptSession
from prompt_toolkit.styles import Style as PromptStyle
import time

# Initialize Rich console and prompt session
console = Console()
prompt_session = PromptSession(
    style=PromptStyle.from_dict({
        'prompt': '#0066ff bold',  # Bright blue prompt
        'completion-menu.completion': 'bg:#1e3a8a fg:#ffffff',
        'completion-menu.completion.current': 'bg:#3b82f6 fg:#ffffff bold',
    })
)

# --------------------------------------------------------------------------------
# 1. Configure OpenAI client and load environment variables
# --------------------------------------------------------------------------------
load_dotenv()  # Load environment variables from .env file
client = OpenAI(
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url="https://api.deepseek.com"
)  # Configure for DeepSeek API

# --------------------------------------------------------------------------------
# 2. Define our schema using Pydantic for type safety
# --------------------------------------------------------------------------------
class FileToCreate(BaseModel):
    path: str
    content: str

class FileToEdit(BaseModel):
    path: str
    original_snippet: str
    new_snippet: str

# Remove AssistantResponse as we're using function calling now

# --------------------------------------------------------------------------------
# 2.1. Define Function Calling Tools
# --------------------------------------------------------------------------------
tools = [
    {
        "type": "function",
        "function": {
            "name": "read_file",
            "description": "Read the content of a single file from the filesystem",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path to the file to read (relative or absolute)",
                    }
                },
                "required": ["file_path"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "read_multiple_files",
            "description": "Read the content of multiple files from the filesystem",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_paths": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Array of file paths to read (relative or absolute)",
                    }
                },
                "required": ["file_paths"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_file",
            "description": "Create a new file or overwrite an existing file with the provided content",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path where the file should be created",
                    },
                    "content": {
                        "type": "string",
                        "description": "The content to write to the file",
                    }
                },
                "required": ["file_path", "content"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_multiple_files",
            "description": "Create multiple files at once",
            "parameters": {
                "type": "object",
                "properties": {
                    "files": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "path": {"type": "string"},
                                "content": {"type": "string"}
                            },
                            "required": ["path", "content"]
                        },
                        "description": "Array of files to create with their paths and content",
                    }
                },
                "required": ["files"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "edit_file",
            "description": "Edit an existing file by replacing a specific snippet with new content",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path to the file to edit",
                    },
                    "original_snippet": {
                        "type": "string",
                        "description": "The exact text snippet to find and replace",
                    },
                    "new_snippet": {
                        "type": "string",
                        "description": "The new text to replace the original snippet with",
                    }
                },
                "required": ["file_path", "original_snippet", "new_snippet"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "detect_project_type",
            "description": "Detect the project type and suggest build/run commands based on configuration files",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "The directory to analyze (defaults to current directory)",
                        "default": "."
                    }
                },
                "required": []
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "install_dependencies",
            "description": "Install project dependencies based on detected project type",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "The project directory (defaults to current directory)",
                        "default": "."
                    },
                    "package_manager": {
                        "type": "string",
                        "description": "Specific package manager to use (auto-detected if not specified)",
                        "enum": ["auto", "pip", "npm", "yarn", "pnpm", "cargo", "go", "maven", "gradle", "dotnet", "uv"]
                    }
                },
                "required": []
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "compile_project",
            "description": "Compile/build the project based on detected project type",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "The project directory (defaults to current directory)",
                        "default": "."
                    },
                    "build_type": {
                        "type": "string",
                        "description": "Build configuration (debug/release)",
                        "enum": ["debug", "release"],
                        "default": "debug"
                    },
                    "custom_command": {
                        "type": "string",
                        "description": "Custom build command to override auto-detection"
                    }
                },
                "required": []
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "run_project",
            "description": "Run the project with appropriate commands based on project type",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "The project directory (defaults to current directory)",
                        "default": "."
                    },
                    "entry_point": {
                        "type": "string",
                        "description": "Specific file or script to run (auto-detected if not specified)"
                    },
                    "arguments": {
                        "type": "string",
                        "description": "Command line arguments to pass to the program"
                    },
                    "custom_command": {
                        "type": "string",
                        "description": "Custom run command to override auto-detection"
                    }
                },
                "required": []
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "run_command",
            "description": "Execute a shell command and return the output",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The shell command to execute"
                    },
                    "directory": {
                        "type": "string",
                        "description": "Working directory for the command (defaults to current directory)",
                        "default": "."
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in seconds (default: 30)",
                        "default": 30
                    }
                },
                "required": ["command"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "open_cmd",
            "description": "Open a new command prompt (cmd) window in the specified directory",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "Directory to open cmd in (defaults to current directory)",
                        "default": "."
                    },
                    "command": {
                        "type": "string",
                        "description": "Optional command to execute in the new cmd window"
                    },
                    "keep_open": {
                        "type": "boolean",
                        "description": "Whether to keep the cmd window open after command execution (default: true)"
                    }
                },
                "required": []
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "start_interactive_cmd",
            "description": "Start an interactive command prompt session that can receive commands and return output",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "Working directory for the cmd session (defaults to current directory)",
                        "default": "."
                    },
                    "session_name": {
                        "type": "string",
                        "description": "Name for this cmd session (for reference in other commands)"
                    }
                },
                "required": []
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "send_cmd_command",
            "description": "Send a command to an active interactive cmd session and get the output",
            "parameters": {
                "type": "object",
                "properties": {
                    "session_name": {
                        "type": "string",
                        "description": "Name of the cmd session to send command to"
                    },
                    "command": {
                        "type": "string",
                        "description": "Command to execute in the cmd session"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in seconds to wait for command completion (default: 30)",
                        "default": 30
                    }
                },
                "required": ["session_name", "command"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "read_cmd_output",
            "description": "Read the current output from an interactive cmd session",
            "parameters": {
                "type": "object",
                "properties": {
                    "session_name": {
                        "type": "string",
                        "description": "Name of the cmd session to read from"
                    }
                },
                "required": ["session_name"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "close_cmd_session",
            "description": "Close an interactive cmd session",
            "parameters": {
                "type": "object",
                "properties": {
                    "session_name": {
                        "type": "string",
                        "description": "Name of the cmd session to close"
                    }
                },
                "required": ["session_name"]
            },
        }
    }
]

# --------------------------------------------------------------------------------
# 3. system prompt
# --------------------------------------------------------------------------------
system_PROMPT = dedent("""\
    You are an windows elite software engineer called DeepSeek Engineer with decades of experience across all programming domains.
    Your expertise spans system design, algorithms, testing, and best practices.
    You provide thoughtful, well-structured solutions while explaining your reasoning.

    Core capabilities:
    1. Code Analysis & Discussion
       - Analyze code with expert-level insight
       - Explain complex concepts clearly
       - Suggest optimizations and best practices
       - Debug issues with precision

    2. File Operations (via function calls):
       - read_file: Read a single file's content
       - read_multiple_files: Read multiple files at once
       - create_file: Create or overwrite a single file
       - create_multiple_files: Create multiple files at once
       - edit_file: Make precise edits to existing files using snippet replacement

    3. Project Operations (via function calls):
       - detect_project_type: Analyze project structure and detect technology stack
       - install_dependencies: Install project dependencies (pip, npm, cargo, etc.)
       - compile_project: Build/compile projects (supports Python, Node.js, Rust, Go, Java, C/C++, C#)
       - run_project: Execute projects with appropriate commands
       - run_command: Execute arbitrary shell commands with output capture
       - open_cmd: Open a new command prompt window in specified directory

    4. Interactive CMD Operations (via function calls):
       - start_interactive_cmd: Start an interactive cmd session for ongoing communication
       - send_cmd_command: Send commands to active cmd session and get output
       - read_cmd_output: Read current output from cmd session
       - close_cmd_session: Close an interactive cmd session

    Guidelines:
    1. Provide natural, conversational responses explaining your reasoning
    2. Use function calls when you need to read or modify files
    3. For file operations:
       - Always read files first before editing them to understand the context
       - Use precise snippet matching for edits
       - Explain what changes you're making and why
       - Consider the impact of changes on the overall codebase
    4. Follow language-specific best practices
    5. Suggest tests or validation steps when appropriate
    6. Be thorough in your analysis and recommendations

    IMPORTANT: In your thinking process, if you realize that something requires a tool call, cut your thinking short and proceed directly to the tool call. Don't overthink - act efficiently when file operations are needed.

    Remember: You're a senior engineer - be thoughtful, precise, and explain your reasoning clearly.
""")

# --------------------------------------------------------------------------------
# 4. Helper functions 
# --------------------------------------------------------------------------------

def read_local_file(file_path: str) -> str:
    """Return the text content of a local file."""
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()

def create_file(path: str, content: str):
    """Create (or overwrite) a file at 'path' with the given 'content'."""
    file_path = Path(path)
    
    # Security checks
    if any(part.startswith('~') for part in file_path.parts):
        raise ValueError("Home directory references not allowed")
    normalized_path = normalize_path(str(file_path))
    
    # Validate reasonable file size for operations
    if len(content) > 5_000_000:  # 5MB limit
        raise ValueError("File content exceeds 5MB size limit")
    
    file_path.parent.mkdir(parents=True, exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)
    console.print(f"[bold blue]✓[/bold blue] Created/updated file at '[bright_cyan]{file_path}[/bright_cyan]'")

def show_diff_table(files_to_edit: List[FileToEdit]) -> None:
    if not files_to_edit:
        return
    
    table = Table(title="📝 Proposed Edits", show_header=True, header_style="bold bright_blue", show_lines=True, border_style="blue")
    table.add_column("File Path", style="bright_cyan", no_wrap=True)
    table.add_column("Original", style="red dim")
    table.add_column("New", style="bright_green")

    for edit in files_to_edit:
        table.add_row(edit.path, edit.original_snippet, edit.new_snippet)
    
    console.print(table)

def apply_diff_edit(path: str, original_snippet: str, new_snippet: str):
    """Reads the file at 'path', replaces the first occurrence of 'original_snippet' with 'new_snippet', then overwrites."""
    try:
        content = read_local_file(path)
        
        # Verify we're replacing the exact intended occurrence
        occurrences = content.count(original_snippet)
        if occurrences == 0:
            raise ValueError("Original snippet not found")
        if occurrences > 1:
            console.print(f"[bold yellow]⚠ Multiple matches ({occurrences}) found - requiring line numbers for safety[/bold yellow]")
            console.print("[dim]Use format:\n--- original.py (lines X-Y)\n+++ modified.py[/dim]")
            raise ValueError(f"Ambiguous edit: {occurrences} matches")
        
        updated_content = content.replace(original_snippet, new_snippet, 1)
        create_file(path, updated_content)
        console.print(f"[bold blue]✓[/bold blue] Applied diff edit to '[bright_cyan]{path}[/bright_cyan]'")

    except FileNotFoundError:
        console.print(f"[bold red]✗[/bold red] File not found for diff editing: '[bright_cyan]{path}[/bright_cyan]'")
    except ValueError as e:
        console.print(f"[bold yellow]⚠[/bold yellow] {str(e)} in '[bright_cyan]{path}[/bright_cyan]'. No changes made.")
        console.print("\n[bold blue]Expected snippet:[/bold blue]")
        console.print(Panel(original_snippet, title="Expected", border_style="blue", title_align="left"))
        console.print("\n[bold blue]Actual file content:[/bold blue]")
        console.print(Panel(content, title="Actual", border_style="yellow", title_align="left"))

def try_handle_add_command(user_input: str) -> bool:
    prefix = "/add "
    if user_input.strip().lower().startswith(prefix):
        path_to_add = user_input[len(prefix):].strip()
        try:
            normalized_path = normalize_path(path_to_add)
            if os.path.isdir(normalized_path):
                # Handle entire directory
                add_directory_to_conversation(normalized_path)
            else:
                # Handle a single file as before
                content = read_local_file(normalized_path)
                conversation_history.append({
                    "role": "system",
                    "content": f"Content of file '{normalized_path}':\n\n{content}"
                })
                console.print(f"[bold blue]✓[/bold blue] Added file '[bright_cyan]{normalized_path}[/bright_cyan]' to conversation.\n")
        except OSError as e:
            console.print(f"[bold red]✗[/bold red] Could not add path '[bright_cyan]{path_to_add}[/bright_cyan]': {e}\n")
        return True
    return False

def add_directory_to_conversation(directory_path: str):
    with console.status("[bold bright_blue]🔍 Scanning directory...[/bold bright_blue]") as status:
        excluded_files = {
            # Python specific
            ".DS_Store", "Thumbs.db", ".gitignore", ".python-version",
            "uv.lock", ".uv", "uvenv", ".uvenv", ".venv", "venv",
            "__pycache__", ".pytest_cache", ".coverage", ".mypy_cache",
            # Node.js / Web specific
            "node_modules", "package-lock.json", "yarn.lock", "pnpm-lock.yaml",
            ".next", ".nuxt", "dist", "build", ".cache", ".parcel-cache",
            ".turbo", ".vercel", ".output", ".contentlayer",
            # Build outputs
            "out", "coverage", ".nyc_output", "storybook-static",
            # Environment and config
            ".env", ".env.local", ".env.development", ".env.production",
            # Misc
            ".git", ".svn", ".hg", "CVS"
        }
        excluded_extensions = {
            # Binary and media files
            ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".webp", ".avif",
            ".mp4", ".webm", ".mov", ".mp3", ".wav", ".ogg",
            ".zip", ".tar", ".gz", ".7z", ".rar",
            ".exe", ".dll", ".so", ".dylib", ".bin",
            # Documents
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            # Python specific
            ".pyc", ".pyo", ".pyd", ".egg", ".whl",
            # UV specific
            ".uv", ".uvenv",
            # Database and logs
            ".db", ".sqlite", ".sqlite3", ".log",
            # IDE specific
            ".idea", ".vscode",
            # Web specific
            ".map", ".chunk.js", ".chunk.css",
            ".min.js", ".min.css", ".bundle.js", ".bundle.css",
            # Cache and temp files
            ".cache", ".tmp", ".temp",
            # Font files
            ".ttf", ".otf", ".woff", ".woff2", ".eot"
        }
        skipped_files = []
        added_files = []
        total_files_processed = 0
        max_files = 1000  # Reasonable limit for files to process
        max_file_size = 5_000_000  # 5MB limit

        for root, dirs, files in os.walk(directory_path):
            if total_files_processed >= max_files:
                console.print(f"[bold yellow]⚠[/bold yellow] Reached maximum file limit ({max_files})")
                break

            status.update(f"[bold bright_blue]🔍 Scanning {root}...[/bold bright_blue]")
            # Skip hidden directories and excluded directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in excluded_files]

            for file in files:
                if total_files_processed >= max_files:
                    break

                if file.startswith('.') or file in excluded_files:
                    skipped_files.append(os.path.join(root, file))
                    continue

                _, ext = os.path.splitext(file)
                if ext.lower() in excluded_extensions:
                    skipped_files.append(os.path.join(root, file))
                    continue

                full_path = os.path.join(root, file)

                try:
                    # Check file size before processing
                    if os.path.getsize(full_path) > max_file_size:
                        skipped_files.append(f"{full_path} (exceeds size limit)")
                        continue

                    # Check if it's binary
                    if is_binary_file(full_path):
                        skipped_files.append(full_path)
                        continue

                    normalized_path = normalize_path(full_path)
                    content = read_local_file(normalized_path)
                    conversation_history.append({
                        "role": "system",
                        "content": f"Content of file '{normalized_path}':\n\n{content}"
                    })
                    added_files.append(normalized_path)
                    total_files_processed += 1

                except OSError:
                    skipped_files.append(full_path)

        console.print(f"[bold blue]✓[/bold blue] Added folder '[bright_cyan]{directory_path}[/bright_cyan]' to conversation.")
        if added_files:
            console.print(f"\n[bold bright_blue]📁 Added files:[/bold bright_blue] [dim]({len(added_files)} of {total_files_processed})[/dim]")
            for f in added_files:
                console.print(f"  [bright_cyan]📄 {f}[/bright_cyan]")
        if skipped_files:
            console.print(f"\n[bold yellow]⏭ Skipped files:[/bold yellow] [dim]({len(skipped_files)})[/dim]")
            for f in skipped_files[:10]:  # Show only first 10 to avoid clutter
                console.print(f"  [yellow dim]⚠ {f}[/yellow dim]")
            if len(skipped_files) > 10:
                console.print(f"  [dim]... and {len(skipped_files) - 10} more[/dim]")
        console.print()

def is_binary_file(file_path: str, peek_size: int = 1024) -> bool:
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(peek_size)
        # If there is a null byte in the sample, treat it as binary
        if b'\0' in chunk:
            return True
        return False
    except Exception:
        # If we fail to read, just treat it as binary to be safe
        return True

def ensure_file_in_context(file_path: str) -> bool:
    try:
        normalized_path = normalize_path(file_path)
        content = read_local_file(normalized_path)
        file_marker = f"Content of file '{normalized_path}'"
        if not any(file_marker in msg["content"] for msg in conversation_history):
            conversation_history.append({
                "role": "system",
                "content": f"{file_marker}:\n\n{content}"
            })
        return True
    except OSError:
        console.print(f"[bold red]✗[/bold red] Could not read file '[bright_cyan]{file_path}[/bright_cyan]' for editing context")
        return False

def normalize_path(path_str: str) -> str:
    """Return a canonical, absolute version of the path with security checks."""
    path = Path(path_str).resolve()

    # Prevent directory traversal attacks
    if ".." in path.parts:
        raise ValueError(f"Invalid path: {path_str} contains parent directory references")

    return str(path)

# --------------------------------------------------------------------------------
# 5. Project Management Helper Functions
# --------------------------------------------------------------------------------

def detect_project_type_impl(directory: str = ".") -> Dict[str, Any]:
    """Detect project type based on configuration files and structure."""
    directory = Path(directory).resolve()

    project_info = {
        "directory": str(directory),
        "type": "unknown",
        "detected_files": [],
        "suggested_commands": {},
        "package_managers": []
    }

    # Check for various project configuration files
    config_files = {
        # Python
        "pyproject.toml": {"type": "python", "pm": ["uv", "pip"], "build": None, "run": "python"},
        "requirements.txt": {"type": "python", "pm": ["pip"], "build": None, "run": "python"},
        "setup.py": {"type": "python", "pm": ["pip"], "build": "python setup.py build", "run": "python"},
        "Pipfile": {"type": "python", "pm": ["pipenv"], "build": None, "run": "pipenv run python"},
        "poetry.lock": {"type": "python", "pm": ["poetry"], "build": None, "run": "poetry run python"},

        # Node.js
        "package.json": {"type": "nodejs", "pm": ["npm", "yarn", "pnpm"], "build": "npm run build", "run": "npm start"},
        "yarn.lock": {"type": "nodejs", "pm": ["yarn"], "build": "yarn build", "run": "yarn start"},
        "pnpm-lock.yaml": {"type": "nodejs", "pm": ["pnpm"], "build": "pnpm build", "run": "pnpm start"},

        # Rust
        "Cargo.toml": {"type": "rust", "pm": ["cargo"], "build": "cargo build", "run": "cargo run"},

        # Go
        "go.mod": {"type": "go", "pm": ["go"], "build": "go build", "run": "go run"},

        # Java
        "pom.xml": {"type": "java", "pm": ["maven"], "build": "mvn compile", "run": "mvn exec:java"},
        "build.gradle": {"type": "java", "pm": ["gradle"], "build": "gradle build", "run": "gradle run"},

        # C/C++
        "Makefile": {"type": "c/cpp", "pm": ["make"], "build": "make", "run": "./main"},
        "CMakeLists.txt": {"type": "c/cpp", "pm": ["cmake"], "build": "cmake . && make", "run": "./main"},

        # C#/.NET
        "*.csproj": {"type": "csharp", "pm": ["dotnet"], "build": "dotnet build", "run": "dotnet run"},
        "*.sln": {"type": "csharp", "pm": ["dotnet"], "build": "dotnet build", "run": "dotnet run"},
    }

    detected_types = []

    for file_pattern, info in config_files.items():
        if "*" in file_pattern:
            # Handle glob patterns
            matches = list(directory.glob(file_pattern))
            if matches:
                project_info["detected_files"].extend([str(f.name) for f in matches])
                detected_types.append(info)
        else:
            file_path = directory / file_pattern
            if file_path.exists():
                project_info["detected_files"].append(file_pattern)
                detected_types.append(info)

    if detected_types:
        # Use the first detected type (could be improved with priority logic)
        primary_type = detected_types[0]
        project_info["type"] = primary_type["type"]
        project_info["package_managers"] = primary_type["pm"]

        # Set suggested commands
        if primary_type["build"]:
            project_info["suggested_commands"]["build"] = primary_type["build"]
        if primary_type["run"]:
            project_info["suggested_commands"]["run"] = primary_type["run"]

        # Add install command based on package manager
        if primary_type["pm"]:
            pm = primary_type["pm"][0]  # Use first package manager
            install_commands = {
                "pip": "pip install -r requirements.txt",
                "uv": "uv sync",
                "npm": "npm install",
                "yarn": "yarn install",
                "pnpm": "pnpm install",
                "cargo": "cargo build",
                "go": "go mod tidy",
                "maven": "mvn install",
                "gradle": "gradle build",
                "dotnet": "dotnet restore",
                "pipenv": "pipenv install",
                "poetry": "poetry install"
            }
            if pm in install_commands:
                project_info["suggested_commands"]["install"] = install_commands[pm]

    return project_info

def execute_command(command: str, directory: str = ".", timeout: int = 30) -> Dict[str, Any]:
    """Execute a shell command and return the result with output."""
    try:
        directory = Path(directory).resolve()

        # Security check
        if not directory.exists():
            return {"success": False, "error": f"Directory does not exist: {directory}"}

        console.print(f"[bold blue]🔧 Executing:[/bold blue] [bright_cyan]{command}[/bright_cyan]")
        console.print(f"[dim]Working directory: {directory}[/dim]")

        # Use shell=True on Windows, False on Unix for better compatibility
        use_shell = os.name == 'nt'

        if use_shell:
            # On Windows, use shell=True
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=directory,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
        else:
            # On Unix, split the command properly
            cmd_parts = shlex.split(command)
            process = subprocess.Popen(
                cmd_parts,
                cwd=directory,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

        output_lines = []

        try:
            # Read output in real-time
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_lines.append(output.strip())
                    console.print(f"[dim]{output.strip()}[/dim]")

            # Wait for process to complete
            return_code = process.wait(timeout=timeout)

            result = {
                "success": return_code == 0,
                "return_code": return_code,
                "output": "\n".join(output_lines),
                "command": command,
                "directory": str(directory)
            }

            if return_code == 0:
                console.print(f"[bold green]✓[/bold green] Command completed successfully")
            else:
                console.print(f"[bold red]✗[/bold red] Command failed with exit code {return_code}")
                result["error"] = f"Command failed with exit code {return_code}"

            return result

        except subprocess.TimeoutExpired:
            process.kill()
            return {
                "success": False,
                "error": f"Command timed out after {timeout} seconds",
                "command": command,
                "directory": str(directory)
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to execute command: {str(e)}",
            "command": command,
            "directory": str(directory)
        }

def open_cmd_window(directory: str = ".", command: str = None, keep_open: bool = True) -> Dict[str, Any]:
    """Open a new command prompt window in the specified directory."""
    try:
        directory = Path(directory).resolve()

        # Security check
        if not directory.exists():
            return {"success": False, "error": f"Directory does not exist: {directory}"}

        console.print(f"[bold blue]🖥️ Opening CMD:[/bold blue] [bright_cyan]{directory}[/bright_cyan]")

        if os.name == 'nt':  # Windows
            if command:
                if keep_open:
                    # Execute command and keep window open
                    cmd_command = f'start cmd /k "cd /d {directory} && {command}"'
                else:
                    # Execute command and close window
                    cmd_command = f'start cmd /c "cd /d {directory} && {command}"'
            else:
                # Just open cmd in the directory
                cmd_command = f'start cmd /k "cd /d {directory}"'

            # Execute the command to open cmd
            result = subprocess.run(cmd_command, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                console.print(f"[bold green]✓[/bold green] CMD window opened successfully")
                return {
                    "success": True,
                    "message": f"CMD window opened in {directory}",
                    "directory": str(directory),
                    "command": command if command else "None"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to open CMD window: {result.stderr}",
                    "directory": str(directory)
                }

        else:  # Unix/Linux/Mac
            # Try different terminal emulators
            terminals = [
                "gnome-terminal",  # GNOME
                "konsole",         # KDE
                "xterm",           # X11
                "terminal",        # macOS
            ]

            for terminal in terminals:
                try:
                    if command:
                        if keep_open:
                            # Execute command and keep terminal open
                            if terminal == "gnome-terminal":
                                cmd_parts = [terminal, f"--working-directory={directory}", "--", "bash", "-c", f"{command}; exec bash"]
                            elif terminal == "konsole":
                                cmd_parts = [terminal, f"--workdir={directory}", "-e", "bash", "-c", f"{command}; exec bash"]
                            elif terminal == "xterm":
                                cmd_parts = [terminal, "-e", "bash", "-c", f"cd {directory} && {command}; exec bash"]
                            else:  # macOS terminal
                                cmd_parts = ["open", "-a", "Terminal", str(directory)]
                        else:
                            # Execute command and close terminal
                            if terminal == "gnome-terminal":
                                cmd_parts = [terminal, f"--working-directory={directory}", "--", "bash", "-c", command]
                            elif terminal == "konsole":
                                cmd_parts = [terminal, f"--workdir={directory}", "-e", "bash", "-c", command]
                            elif terminal == "xterm":
                                cmd_parts = [terminal, "-e", "bash", "-c", f"cd {directory} && {command}"]
                            else:  # macOS terminal
                                cmd_parts = ["open", "-a", "Terminal", str(directory)]
                    else:
                        # Just open terminal in the directory
                        if terminal == "gnome-terminal":
                            cmd_parts = [terminal, f"--working-directory={directory}"]
                        elif terminal == "konsole":
                            cmd_parts = [terminal, f"--workdir={directory}"]
                        elif terminal == "xterm":
                            cmd_parts = [terminal, "-e", "bash", "-c", f"cd {directory}; exec bash"]
                        else:  # macOS terminal
                            cmd_parts = ["open", "-a", "Terminal", str(directory)]

                    result = subprocess.run(cmd_parts, capture_output=True, text=True)

                    if result.returncode == 0:
                        console.print(f"[bold green]✓[/bold green] Terminal window opened successfully using {terminal}")
                        return {
                            "success": True,
                            "message": f"Terminal window opened in {directory} using {terminal}",
                            "directory": str(directory),
                            "command": command if command else "None",
                            "terminal": terminal
                        }

                except FileNotFoundError:
                    continue  # Try next terminal

            return {
                "success": False,
                "error": "No suitable terminal emulator found",
                "directory": str(directory)
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to open terminal: {str(e)}",
            "directory": str(directory)
        }

# --------------------------------------------------------------------------------
# 6. Interactive CMD Session Management
# --------------------------------------------------------------------------------

# Global dictionary to store active cmd sessions
active_cmd_sessions = {}

class CmdSession:
    """Class to manage an interactive cmd session."""

    def __init__(self, name: str, directory: str = "."):
        self.name = name
        self.directory = Path(directory).resolve()
        self.process = None
        self.output_buffer = []
        self.is_active = False

    def start(self) -> Dict[str, Any]:
        """Start the cmd session."""
        try:
            if not self.directory.exists():
                return {"success": False, "error": f"Directory does not exist: {self.directory}"}

            console.print(f"[bold blue]🖥️ Starting interactive CMD session:[/bold blue] [bright_cyan]{self.name}[/bright_cyan]")
            console.print(f"[dim]Working directory: {self.directory}[/dim]")

            if os.name == 'nt':  # Windows
                # Start cmd process with proper settings
                self.process = subprocess.Popen(
                    ['cmd'],
                    cwd=self.directory,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=0,  # Unbuffered
                    universal_newlines=True,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:  # Unix/Linux/Mac
                # Start bash process
                self.process = subprocess.Popen(
                    ['bash'],
                    cwd=self.directory,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=0,  # Unbuffered
                    universal_newlines=True
                )

            self.is_active = True

            # Read initial output (prompt)
            import threading
            import time
            time.sleep(0.1)  # Give cmd time to start
            self._read_available_output()

            console.print(f"[bold green]✓[/bold green] CMD session '{self.name}' started successfully")

            return {
                "success": True,
                "message": f"Interactive CMD session '{self.name}' started in {self.directory}",
                "session_name": self.name,
                "directory": str(self.directory)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to start CMD session: {str(e)}",
                "session_name": self.name
            }

    def send_command(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """Send a command to the cmd session and get output."""
        try:
            if not self.is_active or not self.process:
                return {"success": False, "error": "CMD session is not active"}

            console.print(f"[bold blue]📤 Sending to '{self.name}':[/bold blue] [bright_cyan]{command}[/bright_cyan]")

            # Clear previous output buffer
            self.output_buffer.clear()

            # Send command
            self.process.stdin.write(command + '\n')
            self.process.stdin.flush()

            # Wait for output with timeout
            import time
            start_time = time.time()
            output_lines = []

            while time.time() - start_time < timeout:
                self._read_available_output()
                if self.output_buffer:
                    # Check if we got a prompt back (indicating command completion)
                    recent_output = ''.join(self.output_buffer[-5:])  # Check last 5 lines
                    if '>' in recent_output or '$' in recent_output:
                        break
                time.sleep(0.1)

            # Collect all output
            output = ''.join(self.output_buffer)

            console.print(f"[bold green]✓[/bold green] Command executed in session '{self.name}'")

            return {
                "success": True,
                "output": output,
                "command": command,
                "session_name": self.name
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to send command: {str(e)}",
                "session_name": self.name
            }

    def read_output(self) -> Dict[str, Any]:
        """Read current output from the cmd session."""
        try:
            if not self.is_active or not self.process:
                return {"success": False, "error": "CMD session is not active"}

            self._read_available_output()
            output = ''.join(self.output_buffer)

            return {
                "success": True,
                "output": output,
                "session_name": self.name
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to read output: {str(e)}",
                "session_name": self.name
            }

    def _read_available_output(self):
        """Read all available output from the process."""
        try:
            if self.process and self.process.stdout:
                import select
                import sys

                if os.name == 'nt':  # Windows
                    # On Windows, we need to use a different approach
                    import msvcrt
                    while msvcrt.kbhit() == 0:  # No input available
                        try:
                            line = self.process.stdout.readline()
                            if line:
                                self.output_buffer.append(line)
                            else:
                                break
                        except:
                            break
                else:  # Unix/Linux/Mac
                    # Use select to check for available data
                    ready, _, _ = select.select([self.process.stdout], [], [], 0)
                    if ready:
                        while True:
                            try:
                                line = self.process.stdout.readline()
                                if line:
                                    self.output_buffer.append(line)
                                else:
                                    break
                            except:
                                break
        except:
            pass  # Ignore errors in reading

    def close(self) -> Dict[str, Any]:
        """Close the cmd session."""
        try:
            if self.process:
                self.process.terminate()
                self.process.wait(timeout=5)
                self.process = None

            self.is_active = False
            console.print(f"[bold yellow]🔒[/bold yellow] CMD session '{self.name}' closed")

            return {
                "success": True,
                "message": f"CMD session '{self.name}' closed successfully",
                "session_name": self.name
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to close CMD session: {str(e)}",
                "session_name": self.name
            }

def start_interactive_cmd_impl(directory: str = ".", session_name: str = None) -> Dict[str, Any]:
    """Start an interactive cmd session."""
    if not session_name:
        import time
        session_name = f"cmd_session_{int(time.time())}"

    if session_name in active_cmd_sessions:
        return {"success": False, "error": f"Session '{session_name}' already exists"}

    session = CmdSession(session_name, directory)
    result = session.start()

    if result["success"]:
        active_cmd_sessions[session_name] = session

    return result

def send_cmd_command_impl(session_name: str, command: str, timeout: int = 30) -> Dict[str, Any]:
    """Send a command to an active cmd session."""
    if session_name not in active_cmd_sessions:
        return {"success": False, "error": f"Session '{session_name}' not found"}

    session = active_cmd_sessions[session_name]
    return session.send_command(command, timeout)

def read_cmd_output_impl(session_name: str) -> Dict[str, Any]:
    """Read output from an active cmd session."""
    if session_name not in active_cmd_sessions:
        return {"success": False, "error": f"Session '{session_name}' not found"}

    session = active_cmd_sessions[session_name]
    return session.read_output()

def close_cmd_session_impl(session_name: str) -> Dict[str, Any]:
    """Close an active cmd session."""
    if session_name not in active_cmd_sessions:
        return {"success": False, "error": f"Session '{session_name}' not found"}

    session = active_cmd_sessions[session_name]
    result = session.close()

    if result["success"]:
        del active_cmd_sessions[session_name]

    return result

# --------------------------------------------------------------------------------
# 7. Conversation state
# --------------------------------------------------------------------------------
conversation_history = [
    {"role": "system", "content": system_PROMPT}
]

# --------------------------------------------------------------------------------
# 8. OpenAI API interaction with streaming
# --------------------------------------------------------------------------------

def execute_function_call_dict(tool_call_dict) -> str:
    """Execute a function call from a dictionary format and return the result as a string."""
    try:
        function_name = tool_call_dict["function"]["name"]
        arguments = json.loads(tool_call_dict["function"]["arguments"])
        
        if function_name == "read_file":
            file_path = arguments["file_path"]
            normalized_path = normalize_path(file_path)
            content = read_local_file(normalized_path)
            return f"Content of file '{normalized_path}':\n\n{content}"
            
        elif function_name == "read_multiple_files":
            file_paths = arguments["file_paths"]
            results = []
            for file_path in file_paths:
                try:
                    normalized_path = normalize_path(file_path)
                    content = read_local_file(normalized_path)
                    results.append(f"Content of file '{normalized_path}':\n\n{content}")
                except OSError as e:
                    results.append(f"Error reading '{file_path}': {e}")
            return "\n\n" + "="*50 + "\n\n".join(results)
            
        elif function_name == "create_file":
            file_path = arguments["file_path"]
            content = arguments["content"]
            create_file(file_path, content)
            return f"Successfully created file '{file_path}'"
            
        elif function_name == "create_multiple_files":
            files = arguments["files"]
            created_files = []
            for file_info in files:
                create_file(file_info["path"], file_info["content"])
                created_files.append(file_info["path"])
            return f"Successfully created {len(created_files)} files: {', '.join(created_files)}"
            
        elif function_name == "edit_file":
            file_path = arguments["file_path"]
            original_snippet = arguments["original_snippet"]
            new_snippet = arguments["new_snippet"]

            # Ensure file is in context first
            if not ensure_file_in_context(file_path):
                return f"Error: Could not read file '{file_path}' for editing"

            apply_diff_edit(file_path, original_snippet, new_snippet)
            return f"Successfully edited file '{file_path}'"

        elif function_name == "detect_project_type":
            directory = arguments.get("directory", ".")
            project_info = detect_project_type_impl(directory)

            result = f"Project Analysis for '{project_info['directory']}':\n"
            result += f"Type: {project_info['type']}\n"
            result += f"Detected files: {', '.join(project_info['detected_files'])}\n"
            result += f"Package managers: {', '.join(project_info['package_managers'])}\n"

            if project_info['suggested_commands']:
                result += "\nSuggested commands:\n"
                for cmd_type, cmd in project_info['suggested_commands'].items():
                    result += f"  {cmd_type}: {cmd}\n"

            return result

        elif function_name == "install_dependencies":
            directory = arguments.get("directory", ".")
            package_manager = arguments.get("package_manager", "auto")

            if package_manager == "auto":
                project_info = detect_project_type_impl(directory)
                if project_info['type'] == 'unknown':
                    return f"Could not detect project type in '{directory}'. Please specify package_manager."

                install_cmd = project_info['suggested_commands'].get('install')
                if not install_cmd:
                    return f"No install command available for {project_info['type']} projects."
            else:
                # Use specified package manager
                install_commands = {
                    "pip": "pip install -r requirements.txt",
                    "uv": "uv sync",
                    "npm": "npm install",
                    "yarn": "yarn install",
                    "pnpm": "pnpm install",
                    "cargo": "cargo build",
                    "go": "go mod tidy",
                    "maven": "mvn install",
                    "gradle": "gradle build",
                    "dotnet": "dotnet restore"
                }
                install_cmd = install_commands.get(package_manager)
                if not install_cmd:
                    return f"Unknown package manager: {package_manager}"

            result = execute_command(install_cmd, directory, timeout=300)  # 5 min timeout
            if result['success']:
                return f"Dependencies installed successfully using: {install_cmd}\n\nOutput:\n{result['output']}"
            else:
                return f"Failed to install dependencies: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "compile_project":
            directory = arguments.get("directory", ".")
            build_type = arguments.get("build_type", "debug")
            custom_command = arguments.get("custom_command")

            if custom_command:
                build_cmd = custom_command
            else:
                project_info = detect_project_type_impl(directory)
                if project_info['type'] == 'unknown':
                    return f"Could not detect project type in '{directory}'. Please specify custom_command."

                build_cmd = project_info['suggested_commands'].get('build')
                if not build_cmd:
                    return f"No build command available for {project_info['type']} projects."

                # Modify command for release builds
                if build_type == "release":
                    if "cargo" in build_cmd:
                        build_cmd += " --release"
                    elif "go build" in build_cmd:
                        build_cmd = "go build -ldflags='-s -w'"
                    elif "dotnet" in build_cmd:
                        build_cmd += " --configuration Release"

            result = execute_command(build_cmd, directory, timeout=300)  # 5 min timeout
            if result['success']:
                return f"Project compiled successfully using: {build_cmd}\n\nOutput:\n{result['output']}"
            else:
                return f"Compilation failed: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "run_project":
            directory = arguments.get("directory", ".")
            entry_point = arguments.get("entry_point")
            arguments_str = arguments.get("arguments", "")
            custom_command = arguments.get("custom_command")

            if custom_command:
                run_cmd = custom_command
            else:
                project_info = detect_project_type_impl(directory)
                if project_info['type'] == 'unknown':
                    return f"Could not detect project type in '{directory}'. Please specify custom_command or entry_point."

                run_cmd = project_info['suggested_commands'].get('run')
                if not run_cmd:
                    return f"No run command available for {project_info['type']} projects."

                # Add entry point if specified
                if entry_point:
                    if project_info['type'] == 'python':
                        run_cmd = f"python {entry_point}"
                    elif project_info['type'] == 'nodejs':
                        run_cmd = f"node {entry_point}"
                    elif project_info['type'] == 'go':
                        run_cmd = f"go run {entry_point}"

            # Add arguments if specified
            if arguments_str:
                run_cmd += f" {arguments_str}"

            result = execute_command(run_cmd, directory, timeout=60)  # 1 min timeout for running
            if result['success']:
                return f"Project executed successfully using: {run_cmd}\n\nOutput:\n{result['output']}"
            else:
                return f"Execution failed: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "run_command":
            command = arguments["command"]
            directory = arguments.get("directory", ".")
            timeout = arguments.get("timeout", 30)

            result = execute_command(command, directory, timeout)
            if result['success']:
                return f"Command executed successfully: {command}\n\nOutput:\n{result['output']}"
            else:
                return f"Command failed: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "open_cmd":
            directory = arguments.get("directory", ".")
            command = arguments.get("command")
            keep_open = arguments.get("keep_open", True)

            result = open_cmd_window(directory, command, keep_open)
            if result['success']:
                return f"CMD window opened successfully: {result['message']}"
            else:
                return f"Failed to open CMD window: {result.get('error', 'Unknown error')}"

        elif function_name == "start_interactive_cmd":
            directory = arguments.get("directory", ".")
            session_name = arguments.get("session_name")

            result = start_interactive_cmd_impl(directory, session_name)
            if result['success']:
                return f"Interactive CMD session started: {result['message']}\nSession name: {result['session_name']}"
            else:
                return f"Failed to start CMD session: {result.get('error', 'Unknown error')}"

        elif function_name == "send_cmd_command":
            session_name = arguments["session_name"]
            command = arguments["command"]
            timeout = arguments.get("timeout", 30)

            result = send_cmd_command_impl(session_name, command, timeout)
            if result['success']:
                return f"Command sent to session '{session_name}': {command}\n\nOutput:\n{result['output']}"
            else:
                return f"Failed to send command: {result.get('error', 'Unknown error')}"

        elif function_name == "read_cmd_output":
            session_name = arguments["session_name"]

            result = read_cmd_output_impl(session_name)
            if result['success']:
                return f"Output from session '{session_name}':\n{result['output']}"
            else:
                return f"Failed to read output: {result.get('error', 'Unknown error')}"

        elif function_name == "close_cmd_session":
            session_name = arguments["session_name"]

            result = close_cmd_session_impl(session_name)
            if result['success']:
                return f"CMD session closed: {result['message']}"
            else:
                return f"Failed to close session: {result.get('error', 'Unknown error')}"

        else:
            return f"Unknown function: {function_name}"
            
    except Exception as e:
        return f"Error executing {function_name}: {str(e)}"

def execute_function_call(tool_call) -> str:
    """Execute a function call and return the result as a string."""
    try:
        function_name = tool_call.function.name
        arguments = json.loads(tool_call.function.arguments)
        
        if function_name == "read_file":
            file_path = arguments["file_path"]
            normalized_path = normalize_path(file_path)
            content = read_local_file(normalized_path)
            return f"Content of file '{normalized_path}':\n\n{content}"
            
        elif function_name == "read_multiple_files":
            file_paths = arguments["file_paths"]
            results = []
            for file_path in file_paths:
                try:
                    normalized_path = normalize_path(file_path)
                    content = read_local_file(normalized_path)
                    results.append(f"Content of file '{normalized_path}':\n\n{content}")
                except OSError as e:
                    results.append(f"Error reading '{file_path}': {e}")
            return "\n\n" + "="*50 + "\n\n".join(results)
            
        elif function_name == "create_file":
            file_path = arguments["file_path"]
            content = arguments["content"]
            create_file(file_path, content)
            return f"Successfully created file '{file_path}'"
            
        elif function_name == "create_multiple_files":
            files = arguments["files"]
            created_files = []
            for file_info in files:
                create_file(file_info["path"], file_info["content"])
                created_files.append(file_info["path"])
            return f"Successfully created {len(created_files)} files: {', '.join(created_files)}"
            
        elif function_name == "edit_file":
            file_path = arguments["file_path"]
            original_snippet = arguments["original_snippet"]
            new_snippet = arguments["new_snippet"]

            # Ensure file is in context first
            if not ensure_file_in_context(file_path):
                return f"Error: Could not read file '{file_path}' for editing"

            apply_diff_edit(file_path, original_snippet, new_snippet)
            return f"Successfully edited file '{file_path}'"

        elif function_name == "detect_project_type":
            directory = arguments.get("directory", ".")
            project_info = detect_project_type_impl(directory)

            result = f"Project Analysis for '{project_info['directory']}':\n"
            result += f"Type: {project_info['type']}\n"
            result += f"Detected files: {', '.join(project_info['detected_files'])}\n"
            result += f"Package managers: {', '.join(project_info['package_managers'])}\n"

            if project_info['suggested_commands']:
                result += "\nSuggested commands:\n"
                for cmd_type, cmd in project_info['suggested_commands'].items():
                    result += f"  {cmd_type}: {cmd}\n"

            return result

        elif function_name == "install_dependencies":
            directory = arguments.get("directory", ".")
            package_manager = arguments.get("package_manager", "auto")

            if package_manager == "auto":
                project_info = detect_project_type_impl(directory)
                if project_info['type'] == 'unknown':
                    return f"Could not detect project type in '{directory}'. Please specify package_manager."

                install_cmd = project_info['suggested_commands'].get('install')
                if not install_cmd:
                    return f"No install command available for {project_info['type']} projects."
            else:
                # Use specified package manager
                install_commands = {
                    "pip": "pip install -r requirements.txt",
                    "uv": "uv sync",
                    "npm": "npm install",
                    "yarn": "yarn install",
                    "pnpm": "pnpm install",
                    "cargo": "cargo build",
                    "go": "go mod tidy",
                    "maven": "mvn install",
                    "gradle": "gradle build",
                    "dotnet": "dotnet restore"
                }
                install_cmd = install_commands.get(package_manager)
                if not install_cmd:
                    return f"Unknown package manager: {package_manager}"

            result = execute_command(install_cmd, directory, timeout=300)  # 5 min timeout
            if result['success']:
                return f"Dependencies installed successfully using: {install_cmd}\n\nOutput:\n{result['output']}"
            else:
                return f"Failed to install dependencies: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "compile_project":
            directory = arguments.get("directory", ".")
            build_type = arguments.get("build_type", "debug")
            custom_command = arguments.get("custom_command")

            if custom_command:
                build_cmd = custom_command
            else:
                project_info = detect_project_type_impl(directory)
                if project_info['type'] == 'unknown':
                    return f"Could not detect project type in '{directory}'. Please specify custom_command."

                build_cmd = project_info['suggested_commands'].get('build')
                if not build_cmd:
                    return f"No build command available for {project_info['type']} projects."

                # Modify command for release builds
                if build_type == "release":
                    if "cargo" in build_cmd:
                        build_cmd += " --release"
                    elif "go build" in build_cmd:
                        build_cmd = "go build -ldflags='-s -w'"
                    elif "dotnet" in build_cmd:
                        build_cmd += " --configuration Release"

            result = execute_command(build_cmd, directory, timeout=300)  # 5 min timeout
            if result['success']:
                return f"Project compiled successfully using: {build_cmd}\n\nOutput:\n{result['output']}"
            else:
                return f"Compilation failed: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "run_project":
            directory = arguments.get("directory", ".")
            entry_point = arguments.get("entry_point")
            arguments_str = arguments.get("arguments", "")
            custom_command = arguments.get("custom_command")

            if custom_command:
                run_cmd = custom_command
            else:
                project_info = detect_project_type_impl(directory)
                if project_info['type'] == 'unknown':
                    return f"Could not detect project type in '{directory}'. Please specify custom_command or entry_point."

                run_cmd = project_info['suggested_commands'].get('run')
                if not run_cmd:
                    return f"No run command available for {project_info['type']} projects."

                # Add entry point if specified
                if entry_point:
                    if project_info['type'] == 'python':
                        run_cmd = f"python {entry_point}"
                    elif project_info['type'] == 'nodejs':
                        run_cmd = f"node {entry_point}"
                    elif project_info['type'] == 'go':
                        run_cmd = f"go run {entry_point}"

            # Add arguments if specified
            if arguments_str:
                run_cmd += f" {arguments_str}"

            result = execute_command(run_cmd, directory, timeout=60)  # 1 min timeout for running
            if result['success']:
                return f"Project executed successfully using: {run_cmd}\n\nOutput:\n{result['output']}"
            else:
                return f"Execution failed: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "run_command":
            command = arguments["command"]
            directory = arguments.get("directory", ".")
            timeout = arguments.get("timeout", 30)

            result = execute_command(command, directory, timeout)
            if result['success']:
                return f"Command executed successfully: {command}\n\nOutput:\n{result['output']}"
            else:
                return f"Command failed: {result.get('error', 'Unknown error')}\n\nOutput:\n{result.get('output', '')}"

        elif function_name == "open_cmd":
            directory = arguments.get("directory", ".")
            command = arguments.get("command")
            keep_open = arguments.get("keep_open", True)

            result = open_cmd_window(directory, command, keep_open)
            if result['success']:
                return f"CMD window opened successfully: {result['message']}"
            else:
                return f"Failed to open CMD window: {result.get('error', 'Unknown error')}"

        elif function_name == "start_interactive_cmd":
            directory = arguments.get("directory", ".")
            session_name = arguments.get("session_name")

            result = start_interactive_cmd_impl(directory, session_name)
            if result['success']:
                return f"Interactive CMD session started: {result['message']}\nSession name: {result['session_name']}"
            else:
                return f"Failed to start CMD session: {result.get('error', 'Unknown error')}"

        elif function_name == "send_cmd_command":
            session_name = arguments["session_name"]
            command = arguments["command"]
            timeout = arguments.get("timeout", 30)

            result = send_cmd_command_impl(session_name, command, timeout)
            if result['success']:
                return f"Command sent to session '{session_name}': {command}\n\nOutput:\n{result['output']}"
            else:
                return f"Failed to send command: {result.get('error', 'Unknown error')}"

        elif function_name == "read_cmd_output":
            session_name = arguments["session_name"]

            result = read_cmd_output_impl(session_name)
            if result['success']:
                return f"Output from session '{session_name}':\n{result['output']}"
            else:
                return f"Failed to read output: {result.get('error', 'Unknown error')}"

        elif function_name == "close_cmd_session":
            session_name = arguments["session_name"]

            result = close_cmd_session_impl(session_name)
            if result['success']:
                return f"CMD session closed: {result['message']}"
            else:
                return f"Failed to close session: {result.get('error', 'Unknown error')}"

        else:
            return f"Unknown function: {function_name}"
            
    except Exception as e:
        return f"Error executing {function_name}: {str(e)}"

def trim_conversation_history():
    """Trim conversation history to prevent token limit issues while preserving tool call sequences"""
    if len(conversation_history) <= 20:  # Don't trim if conversation is still small
        return
        
    # Always keep the system prompt
    system_msgs = [msg for msg in conversation_history if msg["role"] == "system"]
    other_msgs = [msg for msg in conversation_history if msg["role"] != "system"]
    
    # Keep only the last 15 messages to prevent token overflow
    if len(other_msgs) > 15:
        other_msgs = other_msgs[-15:]
    
    # Rebuild conversation history
    conversation_history.clear()
    conversation_history.extend(system_msgs + other_msgs)

def stream_openai_response(user_message: str):
    # Add the user message to conversation history
    conversation_history.append({"role": "user", "content": user_message})
    
    # Trim conversation history if it's getting too long
    trim_conversation_history()

    # Remove the old file guessing logic since we'll use function calls
    try:
        stream = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=conversation_history,
            tools=tools,
            max_completion_tokens=64000,
            stream=True
        )

        console.print("\n[bold bright_blue]🐋 Seeking...[/bold bright_blue]")
        reasoning_started = False
        reasoning_content = ""
        final_content = ""
        tool_calls = []

        for chunk in stream:
            # Handle reasoning content if available
            if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                if not reasoning_started:
                    console.print("\n[bold blue]💭 Reasoning:[/bold blue]")
                    reasoning_started = True
                console.print(chunk.choices[0].delta.reasoning_content, end="")
                reasoning_content += chunk.choices[0].delta.reasoning_content
            elif chunk.choices[0].delta.content:
                if reasoning_started:
                    console.print("\n")  # Add spacing after reasoning
                    console.print("\n[bold bright_blue]🤖 Assistant>[/bold bright_blue] ", end="")
                    reasoning_started = False
                final_content += chunk.choices[0].delta.content
                console.print(chunk.choices[0].delta.content, end="")
            elif chunk.choices[0].delta.tool_calls:
                # Handle tool calls
                for tool_call_delta in chunk.choices[0].delta.tool_calls:
                    if tool_call_delta.index is not None:
                        # Ensure we have enough tool_calls
                        while len(tool_calls) <= tool_call_delta.index:
                            tool_calls.append({
                                "id": "",
                                "type": "function",
                                "function": {"name": "", "arguments": ""}
                            })
                        
                        if tool_call_delta.id:
                            tool_calls[tool_call_delta.index]["id"] = tool_call_delta.id
                        if tool_call_delta.function:
                            if tool_call_delta.function.name:
                                tool_calls[tool_call_delta.index]["function"]["name"] += tool_call_delta.function.name
                            if tool_call_delta.function.arguments:
                                tool_calls[tool_call_delta.index]["function"]["arguments"] += tool_call_delta.function.arguments

        console.print()  # New line after streaming

        # Store the assistant's response in conversation history
        assistant_message = {
            "role": "assistant",
            "content": final_content if final_content else None
        }
        
        if tool_calls:
            # Convert our tool_calls format to the expected format
            formatted_tool_calls = []
            for i, tc in enumerate(tool_calls):
                if tc["function"]["name"]:  # Only add if we have a function name
                    # Ensure we have a valid tool call ID
                    tool_id = tc["id"] if tc["id"] else f"call_{i}_{int(time.time() * 1000)}"
                    
                    formatted_tool_calls.append({
                        "id": tool_id,
                        "type": "function",
                        "function": {
                            "name": tc["function"]["name"],
                            "arguments": tc["function"]["arguments"]
                        }
                    })
            
            if formatted_tool_calls:
                # Important: When there are tool calls, content should be None or empty
                if not final_content:
                    assistant_message["content"] = None
                    
                assistant_message["tool_calls"] = formatted_tool_calls
                conversation_history.append(assistant_message)
                
                # Execute tool calls and add results immediately
                console.print(f"\n[bold bright_cyan]⚡ Executing {len(formatted_tool_calls)} function call(s)...[/bold bright_cyan]")
                for tool_call in formatted_tool_calls:
                    console.print(f"[bright_blue]→ {tool_call['function']['name']}[/bright_blue]")
                    
                    try:
                        result = execute_function_call_dict(tool_call)
                        
                        # Add tool result to conversation immediately
                        tool_response = {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": result
                        }
                        conversation_history.append(tool_response)
                    except Exception as e:
                        console.print(f"[red]Error executing {tool_call['function']['name']}: {e}[/red]")
                        # Still need to add a tool response even on error
                        conversation_history.append({
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": f"Error: {str(e)}"
                        })
                
                # Get follow-up response after tool execution
                console.print("\n[bold bright_blue]🔄 Processing results...[/bold bright_blue]")
                
                follow_up_stream = client.chat.completions.create(
                    model="deepseek-reasoner",
                    messages=conversation_history,
                    tools=tools,
                    max_completion_tokens=64000,
                    stream=True
                )
                
                follow_up_content = ""
                reasoning_started = False
                
                for chunk in follow_up_stream:
                    # Handle reasoning content if available
                    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                        if not reasoning_started:
                            console.print("\n[bold blue]💭 Reasoning:[/bold blue]")
                            reasoning_started = True
                        console.print(chunk.choices[0].delta.reasoning_content, end="")
                    elif chunk.choices[0].delta.content:
                        if reasoning_started:
                            console.print("\n")
                            console.print("\n[bold bright_blue]🤖 Assistant>[/bold bright_blue] ", end="")
                            reasoning_started = False
                        follow_up_content += chunk.choices[0].delta.content
                        console.print(chunk.choices[0].delta.content, end="")
                
                console.print()
                
                # Store follow-up response
                conversation_history.append({
                    "role": "assistant",
                    "content": follow_up_content
                })
        else:
            # No tool calls, just store the regular response
            conversation_history.append(assistant_message)

        return {"success": True}

    except Exception as e:
        error_msg = f"DeepSeek API error: {str(e)}"
        console.print(f"\n[bold red]❌ {error_msg}[/bold red]")
        return {"error": error_msg}

# --------------------------------------------------------------------------------
# 8. Main interactive loop
# --------------------------------------------------------------------------------

def main():
    # Create a beautiful gradient-style welcome panel
    welcome_text = """[bold bright_blue]🐋 DeepSeek Engineer[/bold bright_blue] [bright_cyan]with Function Calling[/bright_cyan]
[dim blue]Powered by DeepSeek-R1 with Chain-of-Thought Reasoning[/dim blue]"""
    
    console.print(Panel.fit(
        welcome_text,
        border_style="bright_blue",
        padding=(1, 2),
        title="[bold bright_cyan]🤖 AI Code Assistant[/bold bright_cyan]",
        title_align="center"
    ))
    
    # Create an elegant instruction panel
    instructions = """[bold bright_blue]📁 File Operations:[/bold bright_blue]
  • [bright_cyan]/add path/to/file[/bright_cyan] - Include a single file in conversation
  • [bright_cyan]/add path/to/folder[/bright_cyan] - Include all files in a folder
  • [dim]The AI can automatically read and create files using function calls[/dim]

[bold bright_blue]🔧 Project Operations:[/bold bright_blue]
  • [bright_cyan]Detect project type[/bright_cyan] - Analyze project structure and technology stack
  • [bright_cyan]Install dependencies[/bright_cyan] - Install packages (pip, npm, cargo, etc.)
  • [bright_cyan]Compile/Build projects[/bright_cyan] - Build projects with appropriate tools
  • [bright_cyan]Run projects[/bright_cyan] - Execute projects with correct commands
  • [bright_cyan]Execute shell commands[/bright_cyan] - Run arbitrary commands with output
  • [bright_cyan]Open CMD window[/bright_cyan] - Open command prompt in specified directory

[bold bright_blue]🖥️ Interactive CMD:[/bold bright_blue]
  • [bright_cyan]Start CMD session[/bright_cyan] - Create interactive command prompt session
  • [bright_cyan]Send commands[/bright_cyan] - Execute commands in active CMD session
  • [bright_cyan]Read CMD output[/bright_cyan] - Get output from CMD session
  • [bright_cyan]Close CMD session[/bright_cyan] - Terminate interactive CMD session

[bold bright_blue]🎯 Commands:[/bold bright_blue]
  • [bright_cyan]exit[/bright_cyan] or [bright_cyan]quit[/bright_cyan] - End the session
  • Just ask naturally - the AI will handle operations automatically!"""
    
    console.print(Panel(
        instructions,
        border_style="blue",
        padding=(1, 2),
        title="[bold blue]💡 How to Use[/bold blue]",
        title_align="left"
    ))
    console.print()

    while True:
        try:
            user_input = prompt_session.prompt("🔵 You> ").strip()
        except (EOFError, KeyboardInterrupt):
            console.print("\n[bold yellow]👋 Exiting gracefully...[/bold yellow]")
            break

        if not user_input:
            continue

        if user_input.lower() in ["exit", "quit"]:
            console.print("[bold bright_blue]👋 Goodbye! Happy coding![/bold bright_blue]")
            break

        if try_handle_add_command(user_input):
            continue

        response_data = stream_openai_response(user_input)
        
        if response_data.get("error"):
            console.print(f"[bold red]❌ Error: {response_data['error']}[/bold red]")

    console.print("[bold blue]✨ Session finished. Thank you for using DeepSeek Engineer![/bold blue]")

if __name__ == "__main__":
    main()