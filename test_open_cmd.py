#!/usr/bin/env python3

import os
import sys
from pathlib import Path
from typing import Dict, Any

def open_cmd_window(directory: str = ".", command: str = None, keep_open: bool = True) -> Dict[str, Any]:
    """Open a new command prompt window in the specified directory."""
    try:
        directory = Path(directory).resolve()
        
        # Security check
        if not directory.exists():
            return {"success": False, "error": f"Directory does not exist: {directory}"}
        
        print(f"🖥️ Opening CMD: {directory}")
        
        if os.name == 'nt':  # Windows
            if command:
                if keep_open:
                    # Execute command and keep window open
                    cmd_command = f'start cmd /k "cd /d {directory} && {command}"'
                else:
                    # Execute command and close window
                    cmd_command = f'start cmd /c "cd /d {directory} && {command}"'
            else:
                # Just open cmd in the directory
                cmd_command = f'start cmd /k "cd /d {directory}"'
            
            # Execute the command to open cmd
            import subprocess
            result = subprocess.run(cmd_command, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ CMD window opened successfully")
                return {
                    "success": True,
                    "message": f"CMD window opened in {directory}",
                    "directory": str(directory),
                    "command": command if command else "None"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to open CMD window: {result.stderr}",
                    "directory": str(directory)
                }
        
        else:  # Unix/Linux/Mac
            return {
                "success": False,
                "error": "This test is designed for Windows only",
                "directory": str(directory)
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to open terminal: {str(e)}",
            "directory": str(directory)
        }

def test_open_cmd():
    """Test the open_cmd functionality."""
    print("🔍 Testing Open CMD Functionality")
    print("=" * 50)
    
    # Test 1: Open CMD in current directory
    print("\n📁 Test 1: Opening CMD in current directory")
    result = open_cmd_window(".")
    print(f"Result: {result}")
    
    # Test 2: Open CMD with a command
    print("\n📁 Test 2: Opening CMD with dir command")
    result = open_cmd_window(".", "dir", True)
    print(f"Result: {result}")
    
    print("\n✅ Open CMD test completed!")

if __name__ == "__main__":
    test_open_cmd()
