document.addEventListener('DOMContentLoaded', () => {
    const gameBoard = document.getElementById('game-board');
    const timerDisplay = document.getElementById('timer');
    const scoreDisplay = document.getElementById('score');
    const restartBtn = document.getElementById('restart-btn');
    
    let board = [];
    let selectedTile = null;
    let score = 0;
    let seconds = 0;
    let timer;
    
    // 初始化游戏
    function initGame() {
        gameBoard.innerHTML = '';
        board = [];
        selectedTile = null;
        score = 0;
        seconds = 0;
        scoreDisplay.textContent = score;
        timerDisplay.textContent = seconds;
        
        // 清除之前的定时器
        if (timer) clearInterval(timer);
        timer = setInterval(updateTimer, 1000);
        
        // 创建游戏板
        createBoard();
    }
    
    // 创建游戏板
    function createBoard() {
        const rows = 8;
        const cols = 8;
        const tileTypes = 16; // 方块类型数量
        
        // 生成方块数组（每种类型出现4次）
        let tiles = [];
        for (let i = 0; i < tileTypes; i++) {
            tiles.push(i, i, i, i);
        }
        
        // 随机排序方块
        tiles.sort(() => Math.random() - 0.5);
        
        // 创建游戏板
        for (let i = 0; i < rows; i++) {
            board[i] = [];
            for (let j = 0; j < cols; j++) {
                const tileValue = tiles.pop();
                board[i][j] = tileValue;
                
                const tile = document.createElement('div');
                tile.className = 'tile';
                tile.dataset.row = i;
                tile.dataset.col = j;
                tile.dataset.value = tileValue;
                tile.textContent = tileValue;
                
                // 随机设置不同颜色
                tile.style.backgroundColor = getRandomColor();
                
                tile.addEventListener('click', handleTileClick);
                gameBoard.appendChild(tile);
            }
        }
    }
    
    // 处理方块点击
    function handleTileClick(event) {
        const tile = event.target;
        const row = parseInt(tile.dataset.row);
        const col = parseInt(tile.dataset.col);
        
        // 如果方块已被移除，则忽略
        if (tile.classList.contains('removed')) return;
        
        // 如果没有选中的方块，选中当前方块
        if (!selectedTile) {
            selectedTile = tile;
            tile.classList.add('selected');
            return;
        }
        
        // 如果点击的是同一个方块，取消选中
        if (selectedTile === tile) {
            tile.classList.remove('selected');
            selectedTile = null;
            return;
        }
        
        // 检查两个方块是否匹配
        if (selectedTile.dataset.value === tile.dataset.value) {
            // 检查路径是否连通
            if (isConnected(selectedTile, tile)) {
                // 匹配成功
                selectedTile.classList.add('removed');
                tile.classList.add('removed');
                
                // 增加分数
                score += 10;
                scoreDisplay.textContent = score;
                
                // 检查游戏是否结束
                checkGameEnd();
            }
        }
        
        // 重置选中状态
        selectedTile.classList.remove('selected');
        selectedTile = null;
    }
    
    // 检查两个方块是否连通（简化版）
    function isConnected(tile1, tile2) {
        // 实际实现需要复杂的路径检查算法
        // 这里简化：只允许相邻方块连接
        const row1 = parseInt(tile1.dataset.row);
        const col1 = parseInt(tile1.dataset.col);
        const row2 = parseInt(tile2.dataset.row);
        const col2 = parseInt(tile2.dataset.col);
        
        // 相邻（上、下、左、右）
        return (
            (Math.abs(row1 - row2) === 1 && col1 === col2) ||
            (Math.abs(col1 - col2) === 1 && row1 === row2
        );
    }
    
    // 检查游戏是否结束
    function checkGameEnd() {
        const tiles = document.querySelectorAll('.tile:not(.removed)');
        if (tiles.length === 0) {
            clearInterval(timer);
            alert(`游戏结束！\n得分: ${score}\n用时: ${seconds}秒`);
        }
    }
    
    // 更新计时器
    function updateTimer() {
        seconds++;
        timerDisplay.textContent = seconds;
    }
    
    // 生成随机颜色
    function getRandomColor() {
        const letters = '0123456789ABCDEF';
        let color = '#';
        for (let i = 0; i < 6; i++) {
            color += letters[Math.floor(Math.random() * 16)];
        }
        return color;
    }
    
    // 重新开始游戏
    restartBtn.addEventListener('click', initGame);
    
    // 初始化游戏
    initGame();
});