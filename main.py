import pygame
import sys
import random
from typing import List, Tuple, Optional

# 初始化pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
GRID_SIZE = 10  # 10x10网格
CELL_SIZE = 50
MARGIN = 50
FPS = 60

# 颜色定义
BACKGROUND_COLOR = (30, 30, 50)
GRID_COLOR = (70, 70, 90)
HIGHLIGHT_COLOR = (255, 255, 0)
TEXT_COLOR = (255, 255, 255)

# 图标颜色 - 每种颜色代表一种图标
ICON_COLORS = [
    (255, 0, 0),    # 红
    (0, 255, 0),    # 绿
    (0, 0, 255),    # 蓝
    (255, 255, 0),  # 黄
    (255, 0, 255),  # 紫
    (0, 255, 255),  # 青
    (255, 128, 0),  # 橙
    (128, 0, 255),  # 紫蓝
]

class LinkGame:
    def __init__(self):
        # 创建游戏窗口
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("连连看游戏")
        self.clock = pygame.time.Clock()
        
        # 游戏状态
        self.score = 0
        self.game_over = False
        self.selected_pos = None  # 当前选中的位置 (row, col)
        
        # 初始化游戏板
        self.board = self.generate_board()
        
        # 字体
        self.font = pygame.font.SysFont(None, 36)
    
    def generate_board(self) -> List[List[Optional[int]]]:
        """生成游戏板，确保每种图标成对出现"""
        # 计算需要的图标数量
        total_icons = GRID_SIZE * GRID_SIZE
        
        # 确保是偶数
        if total_icons % 2 != 0:
            total_icons -= 1
        
        # 创建图标列表 (每种图标出现两次)
        icons = []
        for i in range(total_icons // 2):
            color_index = i % len(ICON_COLORS)
            icons.append(color_index)
            icons.append(color_index)
        
        # 随机打乱图标顺序
        random.shuffle(icons)
        
        # 创建游戏板
        board = []
        for i in range(GRID_SIZE):
            row = []
            for j in range(GRID_SIZE):
                if icons:
                    row.append(icons.pop())
                else:
                    row.append(None)  # 空白位置
            board.append(row)
        
        return board
    
    def draw_board(self):
        """绘制游戏板"""
        # 绘制背景
        self.screen.fill(BACKGROUND_COLOR)
        
        # 绘制网格
        for row in range(GRID_SIZE):
            for col in range(GRID_SIZE):
                x = MARGIN + col * CELL_SIZE
                y = MARGIN + row * CELL_SIZE
                
                # 绘制网格单元格
                pygame.draw.rect(self.screen, GRID_COLOR, (x, y, CELL_SIZE, CELL_SIZE), 1)
                
                # 如果有图标，绘制图标
                if self.board[row][col] is not None:
                    color_idx = self.board[row][col]
                    pygame.draw.circle(self.screen, ICON_COLORS[color_idx], 
                                     (x + CELL_SIZE // 2, y + CELL_SIZE // 2), 
                                     CELL_SIZE // 2 - 5)
        
        # 高亮显示选中的图标
        if self.selected_pos:
            row, col = self.selected_pos
            x = MARGIN + col * CELL_SIZE
            y = MARGIN + row * CELL_SIZE
            pygame.draw.rect(self.screen, HIGHLIGHT_COLOR, (x, y, CELL_SIZE, CELL_SIZE), 3)
        
        # 绘制分数
        score_text = self.font.render(f"分数: {self.score}", True, TEXT_COLOR)
        self.screen.blit(score_text, (20, 10))
        
        # 如果游戏结束，显示游戏结束文本
        if self.game_over:
            game_over_text = self.font.render("游戏结束!", True, TEXT_COLOR)
            text_rect = game_over_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
            self.screen.blit(game_over_text, text_rect)
            
            restart_text = self.font.render("按R键重新开始", True, TEXT_COLOR)
            restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 50))
            self.screen.blit(restart_text, restart_rect)
    
    def get_cell_position(self, pos: Tuple[int, int]) -> Optional[Tuple[int, int]]:
        """将屏幕坐标转换为网格位置"""
        x, y = pos
        
        # 检查是否在网格内
        if (x < MARGIN or x >= MARGIN + GRID_SIZE * CELL_SIZE or
            y < MARGIN or y >= MARGIN + GRID_SIZE * CELL_SIZE):
            return None
        
        # 计算网格位置
        col = (x - MARGIN) // CELL_SIZE
        row = (y - MARGIN) // CELL_SIZE
        
        return (row, col)
    
    def is_valid_connection(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> bool:
        """检查两个位置之间是否有有效的连接路径"""
        row1, col1 = pos1
        row2, col2 = pos2
        
        # 如果两个位置相同，无效
        if pos1 == pos2:
            return False
        
        # 如果两个位置的图标不同，无效
        if self.board[row1][col1] != self.board[row2][col2]:
            return False
        
        # 检查直线连接（0个拐点）
        if self.check_straight_line(pos1, pos2):
            return True
        
        # 检查单拐点连接（1个拐点）
        if self.check_one_corner(pos1, pos2):
            return True
        
        # 检查双拐点连接（2个拐点）
        if self.check_two_corners(pos1, pos2):
            return True
        
        return False
    
    def check_straight_line(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> bool:
        """检查是否可以直接直线连接（0个拐点）"""
        row1, col1 = pos1
        row2, col2 = pos2
        
        # 水平线检查
        if row1 == row2:
            start_col = min(col1, col2) + 1
            end_col = max(col1, col2)
            for col in range(start_col, end_col):
                if self.board[row1][col] is not None:
                    return False
            return True
        
        # 垂直线检查
        if col1 == col2:
            start_row = min(row1, row2) + 1
            end_row = max(row1, row2)
            for row in range(start_row, end_row):
                if self.board[row][col1] is not None:
                    return False
            return True
        
        return False
    
    def check_one_corner(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> bool:
        """检查是否可以通过一个拐点连接"""
        row1, col1 = pos1
        row2, col2 = pos2
        
        # 尝试两个可能的拐点
        corner1 = (row1, col2)
        corner2 = (row2, col1)
        
        # 检查第一个拐点
        if self.board[corner1[0]][corner1[1]] is None:
            if (self.check_straight_line(pos1, corner1) and 
                self.check_straight_line(corner1, pos2)):
                return True
        
        # 检查第二个拐点
        if self.board[corner2[0]][corner2[1]] is None:
            if (self.check_straight_line(pos1, corner2) and 
                self.check_straight_line(corner2, pos2)):
                return True
        
        return False
    
    def check_two_corners(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> bool:
        """检查是否可以通过两个拐点连接"""
        row1, col1 = pos1
        row2, col2 = pos2
        
        # 水平扫描
        for col in range(GRID_SIZE):
            if col == col1 or col == col2:
                continue
                
            corner1 = (row1, col)
            corner2 = (row2, col)
            
            # 如果拐点位置为空
            if (self.board[corner1[0]][corner1[1]] is None and
                self.board[corner2[0]][corner2[1]] is None):
                
                # 检查路径
                if (self.check_straight_line(pos1, corner1) and
                    self.check_straight_line(corner1, corner2) and
                    self.check_straight_line(corner2, pos2)):
                    return True
        
        # 垂直扫描
        for row in range(GRID_SIZE):
            if row == row1 or row == row2:
                continue
                
            corner1 = (row, col1)
            corner2 = (row, col2)
            
            # 如果拐点位置为空
            if (self.board[corner1[0]][corner1[1]] is None and
                self.board[corner2[0]][corner2[1]] is None):
                
                # 检查路径
                if (self.check_straight_line(pos1, corner1) and
                    self.check_straight_line(corner1, corner2) and
                    self.check_straight_line(corner2, pos2)):
                    return True
        
        return False
    
    def remove_icons(self, pos1: Tuple[int, int], pos2: Tuple[int, int]):
        """移除两个位置的图标"""
        row1, col1 = pos1
        row2, col2 = pos2
        
        # 计算路径长度作为分数加成
        path_length = abs(row1 - row2) + abs(col1 - col2)
        self.score += 10 + path_length * 2
        
        # 移除图标
        self.board[row1][col1] = None
        self.board[row2][col2] = None
        
        # 检查游戏是否结束
        self.check_game_over()
    
    def check_game_over(self):
        """检查游戏是否结束"""
        # 检查是否所有图标都已消除
        all_cleared = True
        for row in range(GRID_SIZE):
            for col in range(GRID_SIZE):
                if self.board[row][col] is not None:
                    all_cleared = False
                    break
            if not all_cleared:
                break
        
        if all_cleared:
            self.game_over = True
            return
        
        # 检查是否还有可消除的图标对
        for r1 in range(GRID_SIZE):
            for c1 in range(GRID_SIZE):
                if self.board[r1][c1] is None:
                    continue
                
                for r2 in range(GRID_SIZE):
                    for c2 in range(GRID_SIZE):
                        if (r1 == r2 and c1 == c2) or self.board[r2][c2] is None:
                            continue
                        
                        if self.is_valid_connection((r1, c1), (r2, c2)):
                            return  # 找到可消除对，游戏继续
        
        # 没有找到可消除对，游戏结束
        self.game_over = True
    
    def restart_game(self):
        """重新开始游戏"""
        self.score = 0
        self.game_over = False
        self.selected_pos = None
        self.board = self.generate_board()
    
    def run(self):
        """运行游戏主循环"""
        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r:
                        self.restart_game()
                
                if event.type == pygame.MOUSEBUTTONDOWN and not self.game_over:
                    pos = pygame.mouse.get_pos()
                    cell_pos = self.get_cell_position(pos)
                    
                    if cell_pos:
                        row, col = cell_pos
                        # 如果点击的位置有图标
                        if self.board[row][col] is not None:
                            # 如果没有选中的图标，选中当前图标
                            if self.selected_pos is None:
                                self.selected_pos = cell_pos
                            # 如果已经选中了一个图标
                            else:
                                # 如果点击的是同一个图标，取消选中
                                if cell_pos == self.selected_pos:
                                    self.selected_pos = None
                                # 如果点击的是不同的图标
                                else:
                                    # 检查是否可以连接
                                    if self.is_valid_connection(self.selected_pos, cell_pos):
                                        self.remove_icons(self.selected_pos, cell_pos)
                                    self.selected_pos = None
            
            # 绘制游戏
            self.draw_board()
            pygame.display.flip()
            self.clock.tick(FPS)

# 运行游戏
if __name__ == "__main__":
    game = LinkGame()
    game.run()